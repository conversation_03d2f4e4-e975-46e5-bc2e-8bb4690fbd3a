@model CustomerFeedbackAnalyzer.Models.DashboardViewModel
@{
    ViewData["Title"] = "Analytics Dashboard";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-chart-bar me-2"></i>Analytics Dashboard</h2>
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-primary" onclick="refreshDashboard()">
            <i class="fas fa-sync-alt me-1"></i>Refresh
        </button>
        <button type="button" class="btn btn-outline-success" onclick="exportData('csv')">
            <i class="fas fa-download me-1"></i>Export CSV
        </button>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h6>
    </div>
    <div class="card-body">
        <form method="get" asp-action="Index">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Product/Service</label>
                    <select name="SelectedProduct" class="form-select">
                        <option value="">All Products</option>
                        @foreach (var product in ViewBag.Products ?? new List<object>())
                        {
                            <option value="@product" selected="@(Model.SelectedProduct == product.ToString())">@product</option>
                        }
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Category</label>
                    <select name="SelectedCategory" class="form-select">
                        <option value="">All Categories</option>
                        @foreach (var category in ViewBag.Categories ?? new List<object>())
                        {
                            <option value="@category" selected="@(Model.SelectedCategory == category.ToString())">@category</option>
                        }
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Start Date</label>
                    <input type="date" name="StartDate" class="form-control" value="@Model.StartDate?.ToString("yyyy-MM-dd")" />
                </div>
                <div class="col-md-2">
                    <label class="form-label">End Date</label>
                    <input type="date" name="EndDate" class="form-control" value="@Model.EndDate?.ToString("yyyy-MM-dd")" />
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i>Apply
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Key Metrics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Feedback</h6>
                        <h3 class="mb-0">@Model.TotalFeedbacks</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-comments fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Average Rating</h6>
                        <h3 class="mb-0">@Model.OverallAverageRating.ToString("F1")</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-star fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Positive Sentiment</h6>
                        <h3 class="mb-0">@Model.SentimentBreakdown.PositivePercentage%</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-smile fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Products Rated</h6>
                        <h3 class="mb-0">@Model.ProductRatings.Count</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-box fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 1 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Sentiment Breakdown</h6>
            </div>
            <div class="card-body">
                <canvas id="sentimentChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Product Ratings</h6>
            </div>
            <div class="card-body">
                <canvas id="productChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 2 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Category Ratings</h6>
            </div>
            <div class="card-body">
                <canvas id="categoryChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-cloud me-2"></i>Top Keywords</h6>
            </div>
            <div class="card-body">
                <canvas id="keywordChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Feedback -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Feedback</h6>
        <a asp-controller="Feedback" asp-action="List" class="btn btn-sm btn-outline-primary">
            <i class="fas fa-list me-1"></i>View All
        </a>
    </div>
    <div class="card-body">
        @if (Model.RecentFeedbacks.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Customer</th>
                            <th>Product</th>
                            <th>Rating</th>
                            <th>Sentiment</th>
                            <th>Comment</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var feedback in Model.RecentFeedbacks.Take(5))
                        {
                            <tr>
                                <td>@feedback.CustomerName</td>
                                <td>@feedback.Product?.Name</td>
                                <td>
                                    <span class="badge bg-primary">@feedback.Rating ★</span>
                                </td>
                                <td>
                                    <span class="badge @(feedback.Sentiment == "Positive" ? "bg-success" : feedback.Sentiment == "Negative" ? "bg-danger" : "bg-warning")">
                                        @feedback.Sentiment
                                    </span>
                                </td>
                                <td>
                                    <span title="@feedback.Comment">
                                        @(feedback.Comment.Length > 50 ? feedback.Comment.Substring(0, 50) + "..." : feedback.Comment)
                                    </span>
                                </td>
                                <td>@feedback.CreatedAt.ToString("MMM dd, yyyy")</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center text-muted py-4">
                <i class="fas fa-inbox fa-3x mb-3"></i>
                <p>No feedback data available.</p>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        // Chart configurations
        const chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        };

        // Initialize charts
        $(document).ready(function() {
            initializeSentimentChart();
            initializeProductChart();
            initializeCategoryChart();
            initializeKeywordChart();
        });

        function initializeSentimentChart() {
            const ctx = document.getElementById('sentimentChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Positive', 'Neutral', 'Negative'],
                    datasets: [{
                        data: [@Model.SentimentBreakdown.PositiveCount, @Model.SentimentBreakdown.NeutralCount, @Model.SentimentBreakdown.NegativeCount],
                        backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    ...chartOptions,
                    plugins: {
                        ...chartOptions.plugins,
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.parsed;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((value / total) * 100).toFixed(1);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        function initializeProductChart() {
            const ctx = document.getElementById('productChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: [@Html.Raw(Json.Serialize(Model.ProductRatings.Select(p => p.ProductName)))],
                    datasets: [{
                        label: 'Average Rating',
                        data: [@Html.Raw(string.Join(",", Model.ProductRatings.Select(p => p.AverageRating)))],
                        backgroundColor: 'rgba(54, 162, 235, 0.8)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    ...chartOptions,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 5,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
        }

        function initializeCategoryChart() {
            const ctx = document.getElementById('categoryChart').getContext('2d');
            new Chart(ctx, {
                type: 'horizontalBar',
                data: {
                    labels: [@Html.Raw(Json.Serialize(Model.CategoryRatings.Select(c => c.Category)))],
                    datasets: [{
                        label: 'Average Rating',
                        data: [@Html.Raw(string.Join(",", Model.CategoryRatings.Select(c => c.AverageRating)))],
                        backgroundColor: 'rgba(255, 99, 132, 0.8)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    ...chartOptions,
                    indexAxis: 'y',
                    scales: {
                        x: {
                            beginAtZero: true,
                            max: 5,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
        }

        function initializeKeywordChart() {
            const ctx = document.getElementById('keywordChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: [@Html.Raw(Json.Serialize(Model.TopKeywords.Take(10).Select(k => k.Keyword)))],
                    datasets: [{
                        label: 'Frequency',
                        data: [@Html.Raw(string.Join(",", Model.TopKeywords.Take(10).Select(k => k.Frequency)))],
                        backgroundColor: 'rgba(75, 192, 192, 0.8)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    ...chartOptions,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
        }

        function refreshDashboard() {
            location.reload();
        }

        function exportData(format) {
            const params = new URLSearchParams(window.location.search);
            params.set('format', format);
            window.open(`@Url.Action("ExportData", "Dashboard")?${params.toString()}`, '_blank');
        }
    </script>
}
