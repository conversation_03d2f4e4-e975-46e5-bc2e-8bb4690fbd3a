@{
    ViewData["Title"] = "About";
}

<div class="container">
    <!-- Hero Section -->
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-primary mb-3">About Customer Feedback Analyzer</h1>
        <p class="lead text-muted">Transforming customer feedback into actionable business insights through intelligent analysis</p>
    </div>

    <!-- Mission Section -->
    <div class="row mb-5">
        <div class="col-lg-8 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-5 text-center">
                    <i class="fas fa-bullseye fa-3x text-primary mb-4"></i>
                    <h3 class="card-title text-primary mb-3">Our Mission</h3>
                    <p class="card-text lead">
                        To help businesses understand their customers better by providing a comprehensive platform 
                        for collecting, analyzing, and visualizing customer feedback through advanced sentiment analysis 
                        and data visualization techniques.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="row mb-5">
        <div class="col-12">
            <h2 class="text-center mb-5">Key Features</h2>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <i class="fas fa-comments fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">Easy Feedback Collection</h5>
                    <p class="card-text">
                        Simple and intuitive forms for customers to submit their feedback with ratings, 
                        categories, and detailed comments.
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <i class="fas fa-brain fa-3x text-success mb-3"></i>
                    <h5 class="card-title">AI-Powered Analysis</h5>
                    <p class="card-text">
                        Advanced sentiment analysis using VADER algorithm to automatically categorize 
                        feedback as positive, neutral, or negative.
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <i class="fas fa-chart-pie fa-3x text-info mb-3"></i>
                    <h5 class="card-title">Visual Dashboards</h5>
                    <p class="card-text">
                        Interactive charts and graphs to visualize feedback trends, sentiment breakdown, 
                        and product performance metrics.
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <i class="fas fa-search fa-3x text-warning mb-3"></i>
                    <h5 class="card-title">Advanced Filtering</h5>
                    <p class="card-text">
                        Powerful search and filtering capabilities to find specific feedback by product, 
                        category, rating, sentiment, or date range.
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <i class="fas fa-download fa-3x text-secondary mb-3"></i>
                    <h5 class="card-title">Export Capabilities</h5>
                    <p class="card-text">
                        Export feedback data and analytics reports in CSV format for further analysis 
                        and integration with other business tools.
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <i class="fas fa-mobile-alt fa-3x text-danger mb-3"></i>
                    <h5 class="card-title">Mobile Responsive</h5>
                    <p class="card-text">
                        Fully responsive design that works seamlessly across desktop, tablet, 
                        and mobile devices for maximum accessibility.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Technology Stack -->
    <div class="row mb-5">
        <div class="col-12">
            <h2 class="text-center mb-5">Technology Stack</h2>
        </div>
        <div class="col-lg-8 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="row text-center">
                        <div class="col-md-3 mb-3">
                            <i class="fab fa-microsoft fa-3x text-primary mb-2"></i>
                            <h6>.NET 8.0</h6>
                            <small class="text-muted">Backend Framework</small>
                        </div>
                        <div class="col-md-3 mb-3">
                            <i class="fas fa-database fa-3x text-success mb-2"></i>
                            <h6>SQL Server</h6>
                            <small class="text-muted">Database</small>
                        </div>
                        <div class="col-md-3 mb-3">
                            <i class="fab fa-bootstrap fa-3x text-info mb-2"></i>
                            <h6>Bootstrap 5</h6>
                            <small class="text-muted">UI Framework</small>
                        </div>
                        <div class="col-md-3 mb-3">
                            <i class="fas fa-chart-bar fa-3x text-warning mb-2"></i>
                            <h6>Chart.js</h6>
                            <small class="text-muted">Data Visualization</small>
                        </div>
                    </div>
                    <hr class="my-4">
                    <div class="row text-center">
                        <div class="col-md-4 mb-3">
                            <i class="fas fa-robot fa-3x text-secondary mb-2"></i>
                            <h6>VADER Sentiment</h6>
                            <small class="text-muted">Sentiment Analysis</small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <i class="fas fa-shield-alt fa-3x text-danger mb-2"></i>
                            <h6>ASP.NET Identity</h6>
                            <small class="text-muted">Authentication</small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <i class="fas fa-code fa-3x text-dark mb-2"></i>
                            <h6>Entity Framework</h6>
                            <small class="text-muted">ORM</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Benefits Section -->
    <div class="row mb-5">
        <div class="col-12">
            <h2 class="text-center mb-5">Benefits for Your Business</h2>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="d-flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle fa-2x text-success"></i>
                </div>
                <div class="flex-grow-1 ms-3">
                    <h5>Improved Customer Satisfaction</h5>
                    <p class="text-muted">
                        Understand customer pain points and address them proactively to improve overall satisfaction.
                    </p>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="d-flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-chart-line fa-2x text-success"></i>
                </div>
                <div class="flex-grow-1 ms-3">
                    <h5>Data-Driven Decisions</h5>
                    <p class="text-muted">
                        Make informed business decisions based on real customer feedback and sentiment analysis.
                    </p>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="d-flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-clock fa-2x text-success"></i>
                </div>
                <div class="flex-grow-1 ms-3">
                    <h5>Time Efficiency</h5>
                    <p class="text-muted">
                        Automated sentiment analysis saves time on manual feedback review and categorization.
                    </p>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="d-flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-target fa-2x text-success"></i>
                </div>
                <div class="flex-grow-1 ms-3">
                    <h5>Product Improvement</h5>
                    <p class="text-muted">
                        Identify specific areas for product or service improvement based on customer feedback patterns.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Call to Action -->
    <div class="text-center mb-5">
        <div class="card border-0 bg-primary text-white">
            <div class="card-body p-5">
                <h3 class="card-title mb-3">Ready to Get Started?</h3>
                <p class="card-text lead mb-4">
                    Start collecting and analyzing customer feedback today to drive your business forward.
                </p>
                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                    <a asp-controller="Feedback" asp-action="Create" class="btn btn-light btn-lg me-md-2">
                        <i class="fas fa-plus-circle me-2"></i>Submit Feedback
                    </a>
                    <a asp-controller="Dashboard" asp-action="Index" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-chart-bar me-2"></i>View Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
