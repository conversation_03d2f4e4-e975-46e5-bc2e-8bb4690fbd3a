using VaderSharp2;
using System.Text.RegularExpressions;
using CustomerFeedbackAnalyzer.Models;

namespace CustomerFeedbackAnalyzer.Services;

public class SentimentAnalysisService : ISentimentAnalysisService
{
    private readonly SentimentIntensityAnalyzer _analyzer;
    private readonly HashSet<string> _stopWords;

    public SentimentAnalysisService()
    {
        _analyzer = new SentimentIntensityAnalyzer();
        _stopWords = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "a", "an", "and", "are", "as", "at", "be", "by", "for", "from", "has", "he", "in", "is", "it",
            "its", "of", "on", "that", "the", "to", "was", "will", "with", "the", "this", "but", "they",
            "have", "had", "what", "said", "each", "which", "she", "do", "how", "their", "if", "up", "out",
            "many", "then", "them", "these", "so", "some", "her", "would", "make", "like", "into", "him",
            "time", "two", "more", "go", "no", "way", "could", "my", "than", "first", "been", "call", "who",
            "oil", "sit", "now", "find", "down", "day", "did", "get", "come", "made", "may", "part"
        };
    }

    public Task<SentimentResult> AnalyzeSentimentAsync(string text)
    {
        if (string.IsNullOrWhiteSpace(text))
        {
            return Task.FromResult(new SentimentResult
            {
                Sentiment = "Neutral",
                Score = 0.0,
                Confidence = 0.0
            });
        }

        var results = _analyzer.PolarityScores(text);
        
        string sentiment;
        double confidence;
        
        // Determine sentiment based on compound score
        if (results.Compound >= 0.05)
        {
            sentiment = "Positive";
            confidence = Math.Abs(results.Compound);
        }
        else if (results.Compound <= -0.05)
        {
            sentiment = "Negative";
            confidence = Math.Abs(results.Compound);
        }
        else
        {
            sentiment = "Neutral";
            confidence = 1.0 - Math.Abs(results.Compound);
        }

        return Task.FromResult(new SentimentResult
        {
            Sentiment = sentiment,
            Score = results.Compound,
            Confidence = confidence
        });
    }

    public List<string> ExtractKeywords(string text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return new List<string>();

        // Clean and tokenize text
        var cleanText = Regex.Replace(text.ToLower(), @"[^\w\s]", " ");
        var words = cleanText.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);

        // Filter out stop words and short words
        var keywords = words
            .Where(word => word.Length > 2 && !_stopWords.Contains(word))
            .GroupBy(word => word)
            .OrderByDescending(group => group.Count())
            .Take(10)
            .Select(group => group.Key)
            .ToList();

        return keywords;
    }
}
