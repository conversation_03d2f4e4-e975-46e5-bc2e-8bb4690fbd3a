using System.ComponentModel.DataAnnotations;

namespace CustomerFeedbackAnalyzer.Models;

// Feedback Form ViewModel
public class FeedbackViewModel
{
    [Required(ErrorMessage = "Customer name is required")]
    [StringLength(100, ErrorMessage = "Customer name cannot exceed 100 characters")]
    public string CustomerName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Please select a product/service")]
    public int ProductId { get; set; }

    [Required(ErrorMessage = "Please select a category")]
    [StringLength(100)]
    public string Category { get; set; } = string.Empty;

    [Required(ErrorMessage = "Rating is required")]
    [Range(1, 5, ErrorMessage = "Rating must be between 1 and 5")]
    public int Rating { get; set; }

    [Required(ErrorMessage = "Comment is required")]
    [StringLength(2000, MinimumLength = 10, ErrorMessage = "Comment must be between 10 and 2000 characters")]
    public string Comment { get; set; } = string.Empty;

    // For dropdown population
    public List<Product> Products { get; set; } = new List<Product>();
    public List<string> Categories { get; set; } = new List<string>
    {
        "Product Functionality",
        "Quality",
        "Service",
        "User Experience",
        "Performance",
        "Support",
        "Pricing",
        "Other"
    };
}

// Dashboard ViewModel
public class DashboardViewModel
{
    public double OverallAverageRating { get; set; }
    public int TotalFeedbacks { get; set; }
    public SentimentBreakdown SentimentBreakdown { get; set; } = new SentimentBreakdown();
    public List<ProductRating> ProductRatings { get; set; } = new List<ProductRating>();
    public List<Feedback> RecentFeedbacks { get; set; } = new List<Feedback>();
    public List<KeywordFrequency> TopKeywords { get; set; } = new List<KeywordFrequency>();
    public List<CategoryRating> CategoryRatings { get; set; } = new List<CategoryRating>();
    
    // Filter properties
    public string? SelectedProduct { get; set; }
    public string? SelectedCategory { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? SearchTerm { get; set; }
    public string? SortBy { get; set; } = "CreatedAt";
    public string? SortOrder { get; set; } = "desc";
}

public class SentimentBreakdown
{
    public int PositiveCount { get; set; }
    public int NeutralCount { get; set; }
    public int NegativeCount { get; set; }
    public double PositivePercentage { get; set; }
    public double NeutralPercentage { get; set; }
    public double NegativePercentage { get; set; }
}

public class ProductRating
{
    public string ProductName { get; set; } = string.Empty;
    public double AverageRating { get; set; }
    public int FeedbackCount { get; set; }
}

public class CategoryRating
{
    public string Category { get; set; } = string.Empty;
    public double AverageRating { get; set; }
    public int FeedbackCount { get; set; }
}

public class KeywordFrequency
{
    public string Keyword { get; set; } = string.Empty;
    public int Frequency { get; set; }
}

// Login ViewModel
public class LoginViewModel
{
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Password is required")]
    [DataType(DataType.Password)]
    public string Password { get; set; } = string.Empty;

    [Display(Name = "Remember me")]
    public bool RememberMe { get; set; }
}

// Register ViewModel
public class RegisterViewModel
{
    [Required(ErrorMessage = "First name is required")]
    [StringLength(100, ErrorMessage = "First name cannot exceed 100 characters")]
    [Display(Name = "First Name")]
    public string FirstName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Last name is required")]
    [StringLength(100, ErrorMessage = "Last name cannot exceed 100 characters")]
    [Display(Name = "Last Name")]
    public string LastName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Password is required")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "Password must be at least 6 characters long")]
    [DataType(DataType.Password)]
    public string Password { get; set; } = string.Empty;

    [Required(ErrorMessage = "Please confirm your password")]
    [DataType(DataType.Password)]
    [Display(Name = "Confirm Password")]
    [Compare("Password", ErrorMessage = "Password and confirmation password do not match")]
    public string ConfirmPassword { get; set; } = string.Empty;
}

// Feedback List ViewModel for Admin
public class FeedbackListViewModel
{
    public List<Feedback> Feedbacks { get; set; } = new List<Feedback>();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    
    // Filter properties
    public string? SearchTerm { get; set; }
    public string? SelectedProduct { get; set; }
    public string? SelectedCategory { get; set; }
    public int? SelectedRating { get; set; }
    public string? SelectedSentiment { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? SortBy { get; set; } = "CreatedAt";
    public string? SortOrder { get; set; } = "desc";
    
    public List<Product> Products { get; set; } = new List<Product>();
    public List<string> Categories { get; set; } = new List<string>();
    public List<string> Sentiments { get; set; } = new List<string> { "Positive", "Neutral", "Negative" };
}
