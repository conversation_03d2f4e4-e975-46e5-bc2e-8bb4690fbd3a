﻿@{
    ViewData["Title"] = "Customer Feedback Analyzer";
}

<!-- Hero Section -->
<div class="hero-section bg-gradient-primary text-white py-5 mb-5 rounded">
    <div class="container text-center">
        <div class="row align-items-center">
            <div class="col-lg-8 mx-auto">
                <h1 class="display-4 fw-bold mb-4">
                    <i class="fas fa-comments me-3"></i>Customer Feedback Analyzer
                </h1>
                <p class="lead mb-4">
                    Transform customer feedback into actionable insights with our intelligent analysis platform.
                    Collect, analyze, and visualize feedback to drive business improvements.
                </p>
                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                    <a asp-controller="Feedback" asp-action="Create" class="btn btn-light btn-lg me-md-2">
                        <i class="fas fa-plus-circle me-2"></i>Submit Feedback
                    </a>
                    <a asp-controller="Dashboard" asp-action="Index" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-chart-bar me-2"></i>View Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-5">
    <div class="col-md-4">
        <div class="card text-center border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="text-primary mb-3">
                    <i class="fas fa-comments fa-3x"></i>
                </div>
                <h3 class="card-title text-primary">@ViewBag.TotalFeedbacks</h3>
                <p class="card-text text-muted">Total Feedback Collected</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="text-success mb-3">
                    <i class="fas fa-star fa-3x"></i>
                </div>
                <h3 class="card-title text-success">@ViewBag.AverageRating</h3>
                <p class="card-text text-muted">Average Rating</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="text-info mb-3">
                    <i class="fas fa-chart-line fa-3x"></i>
                </div>
                <h3 class="card-title text-info">AI-Powered</h3>
                <p class="card-text text-muted">Sentiment Analysis</p>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="row mb-5">
    <div class="col-lg-6">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body p-4">
                <h5 class="card-title text-primary">
                    <i class="fas fa-magic me-2"></i>Intelligent Analysis
                </h5>
                <p class="card-text">
                    Our advanced sentiment analysis engine automatically categorizes feedback as positive, neutral, or negative,
                    and extracts key insights to help you understand customer sentiment.
                </p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Automated sentiment detection</li>
                    <li><i class="fas fa-check text-success me-2"></i>Keyword extraction</li>
                    <li><i class="fas fa-check text-success me-2"></i>Real-time processing</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body p-4">
                <h5 class="card-title text-success">
                    <i class="fas fa-chart-pie me-2"></i>Visual Dashboards
                </h5>
                <p class="card-text">
                    Get comprehensive insights through interactive charts and visualizations. Track trends,
                    compare products, and make data-driven decisions with our intuitive dashboard.
                </p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Interactive charts</li>
                    <li><i class="fas fa-check text-success me-2"></i>Filtering and sorting</li>
                    <li><i class="fas fa-check text-success me-2"></i>Export capabilities</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Recent Feedback Section -->
@if (ViewBag.RecentFeedbacks != null && ((List<CustomerFeedbackAnalyzer.Models.Feedback>)ViewBag.RecentFeedbacks).Any())
{
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>Recent Feedback
                </h5>
                <a asp-controller="Feedback" asp-action="List" class="btn btn-sm btn-outline-primary">
                    View All <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                @foreach (var feedback in ((List<CustomerFeedbackAnalyzer.Models.Feedback>)ViewBag.RecentFeedbacks))
                {
                    <div class="col-md-6 mb-3">
                        <div class="border rounded p-3 h-100">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0">@feedback.CustomerName</h6>
                                <span class="badge bg-primary">@feedback.Rating ★</span>
                            </div>
                            <p class="text-muted small mb-2">@feedback.Product?.Name</p>
                            <p class="mb-2">@(feedback.Comment.Length > 100 ? feedback.Comment.Substring(0, 100) + "..." : feedback.Comment)</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge @(feedback.Sentiment == "Positive" ? "bg-success" : feedback.Sentiment == "Negative" ? "bg-danger" : "bg-warning")">
                                    @feedback.Sentiment
                                </span>
                                <small class="text-muted">@feedback.CreatedAt.ToString("MMM dd, yyyy")</small>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
}

<style>
    .hero-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
</style>
