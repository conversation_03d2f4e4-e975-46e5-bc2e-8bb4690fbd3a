@model CustomerFeedbackAnalyzer.Models.FeedbackListViewModel
@{
    ViewData["Title"] = "All Feedback";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-list me-2"></i>All Feedback</h2>
    <a asp-action="Create" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>Submit New Feedback
    </a>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-filter me-2"></i>Search & Filter</h6>
    </div>
    <div class="card-body">
        <form method="get" asp-action="List">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Search</label>
                    <input type="text" name="SearchTerm" class="form-control" placeholder="Search comments or customer names..." value="@Model.SearchTerm" />
                </div>
                <div class="col-md-2">
                    <label class="form-label">Product</label>
                    <select name="SelectedProduct" class="form-select">
                        <option value="">All Products</option>
                        @foreach (var product in Model.Products)
                        {
                            <option value="@product.Name" selected="@(Model.SelectedProduct == product.Name)">@product.Name</option>
                        }
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Category</label>
                    <select name="SelectedCategory" class="form-select">
                        <option value="">All Categories</option>
                        @foreach (var category in Model.Categories)
                        {
                            <option value="@category" selected="@(Model.SelectedCategory == category)">@category</option>
                        }
                    </select>
                </div>
                <div class="col-md-1">
                    <label class="form-label">Rating</label>
                    <select name="SelectedRating" class="form-select">
                        <option value="">All</option>
                        @for (int i = 5; i >= 1; i--)
                        {
                            <option value="@i" selected="@(Model.SelectedRating == i)">@i ★</option>
                        }
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Sentiment</label>
                    <select name="SelectedSentiment" class="form-select">
                        <option value="">All Sentiments</option>
                        @foreach (var sentiment in Model.Sentiments)
                        {
                            <option value="@sentiment" selected="@(Model.SelectedSentiment == sentiment)">@sentiment</option>
                        }
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i>Search
                    </button>
                </div>
            </div>
            <div class="row g-3 mt-2">
                <div class="col-md-2">
                    <label class="form-label">Start Date</label>
                    <input type="date" name="StartDate" class="form-control" value="@Model.StartDate?.ToString("yyyy-MM-dd")" />
                </div>
                <div class="col-md-2">
                    <label class="form-label">End Date</label>
                    <input type="date" name="EndDate" class="form-control" value="@Model.EndDate?.ToString("yyyy-MM-dd")" />
                </div>
                <div class="col-md-2">
                    <label class="form-label">Sort By</label>
                    <select name="SortBy" class="form-select">
                        <option value="CreatedAt" selected="@(Model.SortBy == "CreatedAt")">Date</option>
                        <option value="CustomerName" selected="@(Model.SortBy == "CustomerName")">Customer</option>
                        <option value="Rating" selected="@(Model.SortBy == "Rating")">Rating</option>
                        <option value="Sentiment" selected="@(Model.SortBy == "Sentiment")">Sentiment</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Order</label>
                    <select name="SortOrder" class="form-select">
                        <option value="desc" selected="@(Model.SortOrder == "desc")">Descending</option>
                        <option value="asc" selected="@(Model.SortOrder == "asc")">Ascending</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <a asp-action="List" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-times me-1"></i>Clear
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Results Summary -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        <span class="text-muted">
            Showing @((Model.PageNumber - 1) * Model.PageSize + 1) to @(Math.Min(Model.PageNumber * Model.PageSize, Model.TotalCount)) 
            of @Model.TotalCount results
        </span>
    </div>
    <div>
        <select class="form-select form-select-sm" style="width: auto;" onchange="changePageSize(this.value)">
            <option value="10" selected="@(Model.PageSize == 10)">10 per page</option>
            <option value="25" selected="@(Model.PageSize == 25)">25 per page</option>
            <option value="50" selected="@(Model.PageSize == 50)">50 per page</option>
        </select>
    </div>
</div>

<!-- Feedback List -->
@if (Model.Feedbacks.Any())
{
    <div class="row">
        @foreach (var feedback in Model.Feedbacks)
        {
            <div class="col-lg-6 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">@feedback.CustomerName</h6>
                            <small class="text-muted">@feedback.Product?.Name</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-primary">@feedback.Rating ★</span>
                            <br>
                            <span class="badge @(feedback.Sentiment == "Positive" ? "bg-success" : feedback.Sentiment == "Negative" ? "bg-danger" : "bg-warning") mt-1">
                                @feedback.Sentiment
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <span class="badge bg-secondary">@feedback.Category</span>
                        </div>
                        <p class="card-text">@feedback.Comment</p>
                    </div>
                    <div class="card-footer d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>@feedback.CreatedAt.ToString("MMM dd, yyyy HH:mm")
                        </small>
                        <div>
                            <a asp-action="Details" asp-route-id="@feedback.Id" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye me-1"></i>View
                            </a>
                            @if (User.Identity?.IsAuthenticated == true)
                            {
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteFeedback(@feedback.Id)">
                                    <i class="fas fa-trash me-1"></i>Delete
                                </button>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>

    <!-- Pagination -->
    @if (Model.TotalPages > 1)
    {
        <nav aria-label="Feedback pagination">
            <ul class="pagination justify-content-center">
                <li class="page-item @(Model.PageNumber == 1 ? "disabled" : "")">
                    <a class="page-link" href="@GetPageUrl(1)">First</a>
                </li>
                <li class="page-item @(Model.PageNumber == 1 ? "disabled" : "")">
                    <a class="page-link" href="@GetPageUrl(Model.PageNumber - 1)">Previous</a>
                </li>
                
                @for (int i = Math.Max(1, Model.PageNumber - 2); i <= Math.Min(Model.TotalPages, Model.PageNumber + 2); i++)
                {
                    <li class="page-item @(i == Model.PageNumber ? "active" : "")">
                        <a class="page-link" href="@GetPageUrl(i)">@i</a>
                    </li>
                }
                
                <li class="page-item @(Model.PageNumber == Model.TotalPages ? "disabled" : "")">
                    <a class="page-link" href="@GetPageUrl(Model.PageNumber + 1)">Next</a>
                </li>
                <li class="page-item @(Model.PageNumber == Model.TotalPages ? "disabled" : "")">
                    <a class="page-link" href="@GetPageUrl(Model.TotalPages)">Last</a>
                </li>
            </ul>
        </nav>
    }
}
else
{
    <div class="text-center py-5">
        <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
        <h4 class="text-muted">No feedback found</h4>
        <p class="text-muted">Try adjusting your search criteria or submit the first feedback.</p>
        <a asp-action="Create" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>Submit Feedback
        </a>
    </div>
}

@functions {
    private string GetPageUrl(int pageNumber)
    {
        var query = Context.Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
        query["PageNumber"] = pageNumber.ToString();
        return "?" + string.Join("&", query.Select(x => $"{x.Key}={Uri.EscapeDataString(x.Value)}"));
    }
}

@section Scripts {
    <script>
        function changePageSize(pageSize) {
            const url = new URL(window.location);
            url.searchParams.set('PageSize', pageSize);
            url.searchParams.set('PageNumber', '1');
            window.location = url;
        }

        function deleteFeedback(id) {
            if (confirm('Are you sure you want to delete this feedback?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '@Url.Action("Delete")';
                
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'id';
                input.value = id;
                
                const token = document.createElement('input');
                token.type = 'hidden';
                token.name = '__RequestVerificationToken';
                token.value = $('input[name="__RequestVerificationToken"]').val();
                
                form.appendChild(input);
                form.appendChild(token);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
}
