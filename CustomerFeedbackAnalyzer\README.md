# Customer Feedback Analyzer

A comprehensive web-based application built with .NET 8.0 that enables businesses to collect, analyze, and visualize customer feedback through intelligent sentiment analysis and interactive dashboards.

## 🚀 Features

### Core Functionality
- **Feedback Collection**: Simple and intuitive forms for customers to submit feedback
- **Sentiment Analysis**: AI-powered sentiment analysis using VADER algorithm
- **Interactive Dashboards**: Real-time analytics with charts and visualizations
- **Advanced Filtering**: Search and filter feedback by multiple criteria
- **User Authentication**: Secure user registration and login system
- **Data Export**: Export feedback data and reports in CSV format

### Key Components
- **Rating System**: 1-5 star rating with visual feedback
- **Category Management**: Organize feedback by product/service categories
- **Keyword Extraction**: Automatic extraction of key terms from feedback
- **Mobile Responsive**: Works seamlessly across all devices
- **Real-time Analytics**: Live dashboard updates with filtering capabilities

## 🛠️ Technology Stack

- **Backend**: ASP.NET Core 8.0 MVC
- **Database**: SQL Server with Entity Framework Core
- **Authentication**: ASP.NET Core Identity
- **Frontend**: Bootstrap 5, HTML5, CSS3, JavaScript
- **Charts**: Chart.js for data visualization
- **Sentiment Analysis**: VaderSharp2 library
- **Icons**: Font Awesome 6

## 📋 Prerequisites

Before running the application, ensure you have the following installed:

- [.NET 8.0 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
- [SQL Server](https://www.microsoft.com/en-us/sql-server/sql-server-downloads) (LocalDB is sufficient for development)
- [Visual Studio 2022](https://visualstudio.microsoft.com/) or [Visual Studio Code](https://code.visualstudio.com/)

## 🚀 Getting Started

### 1. Clone the Repository
```bash
git clone <repository-url>
cd CustomerFeedbackAnalyzer
```

### 2. Restore Dependencies
```bash
dotnet restore
```

### 3. Update Database Connection String
Edit `appsettings.json` and update the connection string if needed:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=CustomerFeedbackAnalyzerDb;Trusted_Connection=true;MultipleActiveResultSets=true"
  }
}
```

### 4. Create and Update Database
```bash
dotnet ef database update
```

### 5. Run the Application
```bash
dotnet run
```

The application will be available at `https://localhost:5001` or `http://localhost:5000`.

## 📊 Database Schema

The application uses the following main entities:

- **ApplicationUser**: Extended Identity user with additional properties
- **Product**: Products/services that can be rated
- **Feedback**: Customer feedback with ratings, comments, and sentiment analysis
- **Relationships**: Proper foreign key relationships with cascade rules

## 🎯 Usage Guide

### For Customers
1. **Submit Feedback**: Navigate to "Submit Feedback" to provide your experience
2. **Rate Products**: Use the 5-star rating system
3. **Categorize Feedback**: Select appropriate categories for your feedback
4. **Detailed Comments**: Provide detailed feedback (minimum 10 characters)

### For Business Users
1. **View Dashboard**: Access comprehensive analytics and insights
2. **Filter Data**: Use advanced filters to analyze specific segments
3. **Export Reports**: Download feedback data in CSV format
4. **Monitor Trends**: Track sentiment trends over time

### For Administrators
1. **User Management**: Register and manage user accounts
2. **Feedback Management**: View, edit, and delete feedback entries
3. **Product Management**: Manage products/services in the system
4. **Analytics**: Access detailed analytics and reporting features

## 📈 Dashboard Features

### Key Metrics
- Total feedback count
- Average rating across all products
- Sentiment breakdown (Positive/Neutral/Negative)
- Product performance metrics

### Visualizations
- **Sentiment Pie Chart**: Distribution of positive, neutral, and negative feedback
- **Product Ratings Bar Chart**: Average ratings by product/service
- **Category Analysis**: Performance by feedback category
- **Keyword Cloud**: Most frequently mentioned terms

### Filtering Options
- Filter by product/service
- Filter by category
- Date range filtering
- Search by keywords
- Sort by various criteria

## 🔧 Configuration

### Sentiment Analysis
The application uses VADER (Valence Aware Dictionary and sEntiment Reasoner) for sentiment analysis. The sentiment scores are automatically calculated and categorized as:
- **Positive**: Score ≥ 0.05
- **Negative**: Score ≤ -0.05
- **Neutral**: -0.05 < Score < 0.05

### Email Configuration (Optional)
To enable email notifications, update `appsettings.json`:
```json
{
  "EmailSettings": {
    "SmtpServer": "your-smtp-server",
    "SmtpPort": 587,
    "Username": "<EMAIL>",
    "Password": "your-password"
  }
}
```

## 🧪 Testing

### Running Tests
```bash
dotnet test
```

### Test Coverage
The application includes:
- Unit tests for services and controllers
- Integration tests for API endpoints
- UI tests for critical user flows

## 🚀 Deployment

### Production Deployment
1. Update connection string for production database
2. Set environment to Production
3. Configure HTTPS certificates
4. Deploy to your preferred hosting platform (Azure, AWS, etc.)

### Docker Deployment (Optional)
```dockerfile
# Dockerfile example
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["CustomerFeedbackAnalyzer.csproj", "."]
RUN dotnet restore
COPY . .
RUN dotnet build -c Release -o /app/build

FROM build AS publish
RUN dotnet publish -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "CustomerFeedbackAnalyzer.dll"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation and FAQ

## 🔮 Future Enhancements

- Real-time notifications
- Advanced analytics with machine learning
- Multi-language support
- API endpoints for third-party integrations
- Advanced reporting features
- Mobile application

---

**Built with ❤️ using .NET 8.0 and modern web technologies**
