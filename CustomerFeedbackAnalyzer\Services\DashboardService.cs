using Microsoft.EntityFrameworkCore;
using CustomerFeedbackAnalyzer.Data;
using CustomerFeedbackAnalyzer.Models;
using Newtonsoft.Json;

namespace CustomerFeedbackAnalyzer.Services;

public class DashboardService : IDashboardService
{
    private readonly ApplicationDbContext _context;

    public DashboardService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<DashboardViewModel> GetDashboardDataAsync(DashboardViewModel filters)
    {
        var query = _context.Feedbacks.Include(f => f.Product).AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(filters.SelectedProduct))
        {
            query = query.Where(f => f.Product.Name == filters.SelectedProduct);
        }

        if (!string.IsNullOrEmpty(filters.SelectedCategory))
        {
            query = query.Where(f => f.Category == filters.SelectedCategory);
        }

        if (filters.StartDate.HasValue)
        {
            query = query.Where(f => f.CreatedAt >= filters.StartDate.Value);
        }

        if (filters.EndDate.HasValue)
        {
            query = query.Where(f => f.CreatedAt <= filters.EndDate.Value);
        }

        if (!string.IsNullOrEmpty(filters.SearchTerm))
        {
            query = query.Where(f => f.Comment.Contains(filters.SearchTerm) || 
                                   f.CustomerName.Contains(filters.SearchTerm));
        }

        var feedbacks = await query.ToListAsync();

        var dashboard = new DashboardViewModel
        {
            TotalFeedbacks = feedbacks.Count,
            OverallAverageRating = feedbacks.Any() ? feedbacks.Average(f => f.Rating) : 0,
            SentimentBreakdown = CalculateSentimentBreakdown(feedbacks),
            ProductRatings = CalculateProductRatings(feedbacks),
            CategoryRatings = CalculateCategoryRatings(feedbacks),
            RecentFeedbacks = feedbacks.OrderByDescending(f => f.CreatedAt).Take(10).ToList(),
            TopKeywords = ExtractTopKeywords(feedbacks),
            SelectedProduct = filters.SelectedProduct,
            SelectedCategory = filters.SelectedCategory,
            StartDate = filters.StartDate,
            EndDate = filters.EndDate,
            SearchTerm = filters.SearchTerm
        };

        return dashboard;
    }

    public async Task<List<Feedback>> GetFilteredFeedbacksAsync(FeedbackListViewModel filters)
    {
        var query = _context.Feedbacks.Include(f => f.Product).AsQueryable();

        // Apply filters
        query = ApplyFilters(query, filters);

        // Apply sorting
        query = ApplySorting(query, filters.SortBy, filters.SortOrder);

        // Apply pagination
        var feedbacks = await query
            .Skip((filters.PageNumber - 1) * filters.PageSize)
            .Take(filters.PageSize)
            .ToListAsync();

        return feedbacks;
    }

    public async Task<int> GetFilteredFeedbackCountAsync(FeedbackListViewModel filters)
    {
        var query = _context.Feedbacks.Include(f => f.Product).AsQueryable();
        query = ApplyFilters(query, filters);
        return await query.CountAsync();
    }

    private IQueryable<Feedback> ApplyFilters(IQueryable<Feedback> query, FeedbackListViewModel filters)
    {
        if (!string.IsNullOrEmpty(filters.SearchTerm))
        {
            query = query.Where(f => f.Comment.Contains(filters.SearchTerm) || 
                                   f.CustomerName.Contains(filters.SearchTerm));
        }

        if (!string.IsNullOrEmpty(filters.SelectedProduct))
        {
            query = query.Where(f => f.Product.Name == filters.SelectedProduct);
        }

        if (!string.IsNullOrEmpty(filters.SelectedCategory))
        {
            query = query.Where(f => f.Category == filters.SelectedCategory);
        }

        if (filters.SelectedRating.HasValue)
        {
            query = query.Where(f => f.Rating == filters.SelectedRating.Value);
        }

        if (!string.IsNullOrEmpty(filters.SelectedSentiment))
        {
            query = query.Where(f => f.Sentiment == filters.SelectedSentiment);
        }

        if (filters.StartDate.HasValue)
        {
            query = query.Where(f => f.CreatedAt >= filters.StartDate.Value);
        }

        if (filters.EndDate.HasValue)
        {
            query = query.Where(f => f.CreatedAt <= filters.EndDate.Value);
        }

        return query;
    }

    private IQueryable<Feedback> ApplySorting(IQueryable<Feedback> query, string? sortBy, string? sortOrder)
    {
        var isDescending = sortOrder?.ToLower() == "desc";

        return sortBy?.ToLower() switch
        {
            "customername" => isDescending ? query.OrderByDescending(f => f.CustomerName) : query.OrderBy(f => f.CustomerName),
            "product" => isDescending ? query.OrderByDescending(f => f.Product.Name) : query.OrderBy(f => f.Product.Name),
            "category" => isDescending ? query.OrderByDescending(f => f.Category) : query.OrderBy(f => f.Category),
            "rating" => isDescending ? query.OrderByDescending(f => f.Rating) : query.OrderBy(f => f.Rating),
            "sentiment" => isDescending ? query.OrderByDescending(f => f.Sentiment) : query.OrderBy(f => f.Sentiment),
            _ => isDescending ? query.OrderByDescending(f => f.CreatedAt) : query.OrderBy(f => f.CreatedAt)
        };
    }

    private SentimentBreakdown CalculateSentimentBreakdown(List<Feedback> feedbacks)
    {
        var total = feedbacks.Count;
        if (total == 0)
        {
            return new SentimentBreakdown();
        }

        var positive = feedbacks.Count(f => f.Sentiment == "Positive");
        var neutral = feedbacks.Count(f => f.Sentiment == "Neutral");
        var negative = feedbacks.Count(f => f.Sentiment == "Negative");

        return new SentimentBreakdown
        {
            PositiveCount = positive,
            NeutralCount = neutral,
            NegativeCount = negative,
            PositivePercentage = Math.Round((double)positive / total * 100, 1),
            NeutralPercentage = Math.Round((double)neutral / total * 100, 1),
            NegativePercentage = Math.Round((double)negative / total * 100, 1)
        };
    }

    private List<ProductRating> CalculateProductRatings(List<Feedback> feedbacks)
    {
        return feedbacks
            .GroupBy(f => f.Product.Name)
            .Select(g => new ProductRating
            {
                ProductName = g.Key,
                AverageRating = Math.Round(g.Average(f => f.Rating), 1),
                FeedbackCount = g.Count()
            })
            .OrderByDescending(pr => pr.AverageRating)
            .ToList();
    }

    private List<CategoryRating> CalculateCategoryRatings(List<Feedback> feedbacks)
    {
        return feedbacks
            .GroupBy(f => f.Category)
            .Select(g => new CategoryRating
            {
                Category = g.Key,
                AverageRating = Math.Round(g.Average(f => f.Rating), 1),
                FeedbackCount = g.Count()
            })
            .OrderByDescending(cr => cr.AverageRating)
            .ToList();
    }

    private List<KeywordFrequency> ExtractTopKeywords(List<Feedback> feedbacks)
    {
        var allKeywords = new List<string>();

        foreach (var feedback in feedbacks)
        {
            if (!string.IsNullOrEmpty(feedback.Keywords))
            {
                try
                {
                    var keywords = JsonConvert.DeserializeObject<List<string>>(feedback.Keywords);
                    if (keywords != null)
                    {
                        allKeywords.AddRange(keywords);
                    }
                }
                catch
                {
                    // Handle JSON parsing errors gracefully
                }
            }
        }

        return allKeywords
            .GroupBy(k => k)
            .Select(g => new KeywordFrequency
            {
                Keyword = g.Key,
                Frequency = g.Count()
            })
            .OrderByDescending(kf => kf.Frequency)
            .Take(20)
            .ToList();
    }
}
