@model CustomerFeedbackAnalyzer.Models.RegisterViewModel
@{
    ViewData["Title"] = "Register";
}

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header bg-success text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>Create Account
                </h4>
            </div>
            <div class="card-body p-4">
                <form asp-action="Register" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="FirstName" class="form-label">
                                <i class="fas fa-user me-1"></i>First Name
                            </label>
                            <input asp-for="FirstName" class="form-control" placeholder="Enter your first name" />
                            <span asp-validation-for="FirstName" class="text-danger"></span>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label asp-for="LastName" class="form-label">
                                <i class="fas fa-user me-1"></i>Last Name
                            </label>
                            <input asp-for="LastName" class="form-control" placeholder="Enter your last name" />
                            <span asp-validation-for="LastName" class="text-danger"></span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>Email Address
                        </label>
                        <input asp-for="Email" class="form-control" placeholder="Enter your email address" />
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Password" class="form-label">
                            <i class="fas fa-lock me-1"></i>Password
                        </label>
                        <input asp-for="Password" class="form-control" placeholder="Create a password" />
                        <span asp-validation-for="Password" class="text-danger"></span>
                        <div class="form-text">Password must be at least 6 characters long and contain uppercase, lowercase, and numeric characters.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="ConfirmPassword" class="form-label">
                            <i class="fas fa-lock me-1"></i>Confirm Password
                        </label>
                        <input asp-for="ConfirmPassword" class="form-control" placeholder="Confirm your password" />
                        <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-user-plus me-1"></i>Create Account
                        </button>
                    </div>
                </form>
                
                <hr class="my-4">
                
                <div class="text-center">
                    <p class="mb-0">Already have an account?</p>
                    <a asp-action="Login" class="btn btn-outline-primary">
                        <i class="fas fa-sign-in-alt me-1"></i>Login Here
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
