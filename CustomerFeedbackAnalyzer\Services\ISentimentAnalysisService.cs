using CustomerFeedbackAnalyzer.Models;

namespace CustomerFeedbackAnalyzer.Services;

public interface ISentimentAnalysisService
{
    Task<SentimentResult> AnalyzeSentimentAsync(string text);
    List<string> ExtractKeywords(string text);
}

public class SentimentResult
{
    public string Sentiment { get; set; } = string.Empty; // Positive, Neutral, Negative
    public double Score { get; set; }
    public double Confidence { get; set; }
}
