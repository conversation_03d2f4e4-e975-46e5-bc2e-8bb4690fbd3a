using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using CustomerFeedbackAnalyzer.Data;
using CustomerFeedbackAnalyzer.Models;
using CustomerFeedbackAnalyzer.Services;

namespace CustomerFeedbackAnalyzer.Controllers;

public class DashboardController : Controller
{
    private readonly ApplicationDbContext _context;
    private readonly IDashboardService _dashboardService;
    private readonly ILogger<DashboardController> _logger;

    public DashboardController(
        ApplicationDbContext context,
        IDashboardService dashboardService,
        ILogger<DashboardController> logger)
    {
        _context = context;
        _dashboardService = dashboardService;
        _logger = logger;
    }

    public async Task<IActionResult> Index(DashboardViewModel model)
    {
        try
        {
            var dashboard = await _dashboardService.GetDashboardDataAsync(model);
            return View(dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading dashboard");
            return View(new DashboardViewModel());
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetChartData(string chartType, string? product = null, string? category = null, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var filters = new DashboardViewModel
            {
                SelectedProduct = product,
                SelectedCategory = category,
                StartDate = startDate,
                EndDate = endDate
            };

            var dashboard = await _dashboardService.GetDashboardDataAsync(filters);

            return chartType.ToLower() switch
            {
                "sentiment" => Json(new
                {
                    labels = new[] { "Positive", "Neutral", "Negative" },
                    data = new[] { dashboard.SentimentBreakdown.PositiveCount, dashboard.SentimentBreakdown.NeutralCount, dashboard.SentimentBreakdown.NegativeCount },
                    backgroundColor = new[] { "#28a745", "#ffc107", "#dc3545" }
                }),
                "products" => Json(new
                {
                    labels = dashboard.ProductRatings.Select(p => p.ProductName).ToArray(),
                    data = dashboard.ProductRatings.Select(p => p.AverageRating).ToArray(),
                    backgroundColor = GenerateColors(dashboard.ProductRatings.Count)
                }),
                "categories" => Json(new
                {
                    labels = dashboard.CategoryRatings.Select(c => c.Category).ToArray(),
                    data = dashboard.CategoryRatings.Select(c => c.AverageRating).ToArray(),
                    backgroundColor = GenerateColors(dashboard.CategoryRatings.Count)
                }),
                "keywords" => Json(new
                {
                    labels = dashboard.TopKeywords.Take(10).Select(k => k.Keyword).ToArray(),
                    data = dashboard.TopKeywords.Take(10).Select(k => k.Frequency).ToArray(),
                    backgroundColor = GenerateColors(Math.Min(10, dashboard.TopKeywords.Count))
                }),
                _ => Json(new { error = "Invalid chart type" })
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting chart data for {ChartType}", chartType);
            return Json(new { error = "Error loading chart data" });
        }
    }

    [HttpGet]
    public async Task<IActionResult> ExportData(string format = "csv", string? product = null, string? category = null, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var filters = new DashboardViewModel
            {
                SelectedProduct = product,
                SelectedCategory = category,
                StartDate = startDate,
                EndDate = endDate
            };

            var dashboard = await _dashboardService.GetDashboardDataAsync(filters);

            if (format.ToLower() == "csv")
            {
                var csv = GenerateCsvReport(dashboard);
                var bytes = System.Text.Encoding.UTF8.GetBytes(csv);
                return File(bytes, "text/csv", $"feedback-report-{DateTime.Now:yyyyMMdd}.csv");
            }

            return BadRequest("Unsupported format");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting data");
            return BadRequest("Error exporting data");
        }
    }

    private string[] GenerateColors(int count)
    {
        var colors = new[]
        {
            "#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF",
            "#FF9F40", "#FF6384", "#C9CBCF", "#4BC0C0", "#FF6384"
        };

        return colors.Take(count).ToArray();
    }

    private string GenerateCsvReport(DashboardViewModel dashboard)
    {
        var csv = new System.Text.StringBuilder();
        
        // Header
        csv.AppendLine("Customer Name,Product,Category,Rating,Sentiment,Comment,Date");
        
        // Data rows
        foreach (var feedback in dashboard.RecentFeedbacks)
        {
            csv.AppendLine($"\"{feedback.CustomerName}\",\"{feedback.Product?.Name}\",\"{feedback.Category}\",{feedback.Rating},\"{feedback.Sentiment}\",\"{feedback.Comment.Replace("\"", "\"\"")}\",\"{feedback.CreatedAt:yyyy-MM-dd HH:mm:ss}\"");
        }

        return csv.ToString();
    }
}
