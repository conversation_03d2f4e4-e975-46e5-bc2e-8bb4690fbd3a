using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using CustomerFeedbackAnalyzer.Data;
using CustomerFeedbackAnalyzer.Models;
using CustomerFeedbackAnalyzer.Services;

namespace CustomerFeedbackAnalyzer.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly ApplicationDbContext _context;
    private readonly IDashboardService _dashboardService;

    public HomeController(
        ILogger<HomeController> logger,
        ApplicationDbContext context,
        IDashboardService dashboardService)
    {
        _logger = logger;
        _context = context;
        _dashboardService = dashboardService;
    }

    public async Task<IActionResult> Index()
    {
        try
        {
            // Get basic stats for home page
            var totalFeedbacks = await _context.Feedbacks.CountAsync();
            var averageRating = await _context.Feedbacks.AverageAsync(f => (double?)f.Rating) ?? 0;
            var recentFeedbacks = await _context.Feedbacks
                .Include(f => f.Product)
                .OrderByDescending(f => f.CreatedAt)
                .Take(5)
                .ToListAsync();

            ViewBag.TotalFeedbacks = totalFeedbacks;
            ViewBag.AverageRating = Math.Round(averageRating, 1);
            ViewBag.RecentFeedbacks = recentFeedbacks;

            return View();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading home page");
            return View();
        }
    }

    public IActionResult Privacy()
    {
        return View();
    }

    public IActionResult About()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
