using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using CustomerFeedbackAnalyzer.Data;
using CustomerFeedbackAnalyzer.Models;
using CustomerFeedbackAnalyzer.Services;
using Newtonsoft.Json;
using System.Security.Claims;

namespace CustomerFeedbackAnalyzer.Controllers;

public class FeedbackController : Controller
{
    private readonly ApplicationDbContext _context;
    private readonly ISentimentAnalysisService _sentimentService;
    private readonly IDashboardService _dashboardService;
    private readonly ILogger<FeedbackController> _logger;

    public FeedbackController(
        ApplicationDbContext context,
        ISentimentAnalysisService sentimentService,
        IDashboardService dashboardService,
        ILogger<FeedbackController> logger)
    {
        _context = context;
        _sentimentService = sentimentService;
        _dashboardService = dashboardService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<IActionResult> Create()
    {
        var model = new FeedbackViewModel
        {
            Products = await _context.Products.Where(p => p.IsActive).ToListAsync()
        };

        return View(model);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(FeedbackViewModel model)
    {
        if (ModelState.IsValid)
        {
            try
            {
                // Perform sentiment analysis
                var sentimentResult = await _sentimentService.AnalyzeSentimentAsync(model.Comment);
                var keywords = _sentimentService.ExtractKeywords(model.Comment);

                var feedback = new Feedback
                {
                    CustomerName = model.CustomerName,
                    ProductId = model.ProductId,
                    Category = model.Category,
                    Rating = model.Rating,
                    Comment = model.Comment,
                    Sentiment = sentimentResult.Sentiment,
                    SentimentScore = sentimentResult.Score,
                    Keywords = JsonConvert.SerializeObject(keywords),
                    CreatedAt = DateTime.UtcNow,
                    UserId = User.Identity?.IsAuthenticated == true ? User.FindFirstValue(ClaimTypes.NameIdentifier) : null
                };

                _context.Feedbacks.Add(feedback);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "Thank you for your feedback! Your submission has been recorded.";
                return RedirectToAction("Success");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating feedback");
                ModelState.AddModelError("", "An error occurred while submitting your feedback. Please try again.");
            }
        }

        // Reload products for dropdown if validation fails
        model.Products = await _context.Products.Where(p => p.IsActive).ToListAsync();
        return View(model);
    }

    public IActionResult Success()
    {
        return View();
    }

    [HttpGet]
    public async Task<IActionResult> List(FeedbackListViewModel model)
    {
        // Set default values
        model.PageNumber = model.PageNumber < 1 ? 1 : model.PageNumber;
        model.PageSize = model.PageSize < 1 ? 10 : model.PageSize;

        // Get filtered feedbacks
        model.Feedbacks = await _dashboardService.GetFilteredFeedbacksAsync(model);
        model.TotalCount = await _dashboardService.GetFilteredFeedbackCountAsync(model);

        // Load filter options
        model.Products = await _context.Products.Where(p => p.IsActive).ToListAsync();
        model.Categories = await _context.Feedbacks
            .Select(f => f.Category)
            .Distinct()
            .OrderBy(c => c)
            .ToListAsync();

        return View(model);
    }

    [HttpGet]
    public async Task<IActionResult> Details(int id)
    {
        var feedback = await _context.Feedbacks
            .Include(f => f.Product)
            .Include(f => f.User)
            .FirstOrDefaultAsync(f => f.Id == id);

        if (feedback == null)
        {
            return NotFound();
        }

        return View(feedback);
    }

    [HttpPost]
    public async Task<IActionResult> Delete(int id)
    {
        var feedback = await _context.Feedbacks.FindAsync(id);
        if (feedback != null)
        {
            _context.Feedbacks.Remove(feedback);
            await _context.SaveChangesAsync();
            TempData["SuccessMessage"] = "Feedback deleted successfully.";
        }

        return RedirectToAction("List");
    }
}
