using CustomerFeedbackAnalyzer.Data;
using CustomerFeedbackAnalyzer.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace CustomerFeedbackAnalyzer.Services;

public class DataSeedingService
{
    private readonly ApplicationDbContext _context;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ISentimentAnalysisService _sentimentService;

    public DataSeedingService(
        ApplicationDbContext context,
        UserManager<ApplicationUser> userManager,
        ISentimentAnalysisService sentimentService)
    {
        _context = context;
        _userManager = userManager;
        _sentimentService = sentimentService;
    }

    public async Task SeedSampleDataAsync()
    {
        // Check if we already have feedback data
        if (_context.Feedbacks.Any())
        {
            return; // Data already seeded
        }

        // Create sample users
        var users = await CreateSampleUsersAsync();

        // Create sample feedback
        await CreateSampleFeedbackAsync(users);
    }

    private async Task<List<ApplicationUser>> CreateSampleUsersAsync()
    {
        var users = new List<ApplicationUser>();

        var sampleUsers = new[]
        {
            new { Email = "<EMAIL>", FirstName = "John", LastName = "Doe" },
            new { Email = "<EMAIL>", FirstName = "Jane", LastName = "Smith" },
            new { Email = "<EMAIL>", FirstName = "Mike", LastName = "Johnson" },
            new { Email = "<EMAIL>", FirstName = "Sarah", LastName = "Wilson" },
            new { Email = "<EMAIL>", FirstName = "David", LastName = "Brown" }
        };

        foreach (var userData in sampleUsers)
        {
            var existingUser = await _userManager.FindByEmailAsync(userData.Email);
            if (existingUser == null)
            {
                var user = new ApplicationUser
                {
                    UserName = userData.Email,
                    Email = userData.Email,
                    FirstName = userData.FirstName,
                    LastName = userData.LastName,
                    EmailConfirmed = true,
                    CreatedAt = DateTime.UtcNow.AddDays(-Random.Shared.Next(30, 365))
                };

                var result = await _userManager.CreateAsync(user, "Password123!");
                if (result.Succeeded)
                {
                    users.Add(user);
                }
            }
            else
            {
                users.Add(existingUser);
            }
        }

        return users;
    }

    private async Task CreateSampleFeedbackAsync(List<ApplicationUser> users)
    {
        var products = await _context.Products.ToListAsync();
        var categories = new[] { "Product Functionality", "Quality", "Service", "User Experience", "Performance", "Support", "Pricing" };

        var sampleFeedbacks = new[]
        {
            new { CustomerName = "Alice Johnson", Comment = "Excellent service! The mobile app is very user-friendly and fast. I love the new features.", Rating = 5, Category = "User Experience" },
            new { CustomerName = "Bob Smith", Comment = "Good overall experience, but the payment system could be improved. Sometimes it's slow.", Rating = 4, Category = "Performance" },
            new { CustomerName = "Carol Davis", Comment = "The customer support team was very helpful and resolved my issue quickly. Great job!", Rating = 5, Category = "Support" },
            new { CustomerName = "Daniel Wilson", Comment = "Average service. The web platform works fine but nothing exceptional. Could use more features.", Rating = 3, Category = "Product Functionality" },
            new { CustomerName = "Emma Brown", Comment = "Poor delivery experience. The package arrived late and was damaged. Very disappointed.", Rating = 2, Category = "Service" },
            new { CustomerName = "Frank Miller", Comment = "Outstanding quality! The product exceeded my expectations. Will definitely recommend to others.", Rating = 5, Category = "Quality" },
            new { CustomerName = "Grace Taylor", Comment = "The pricing is reasonable for the quality provided. Good value for money.", Rating = 4, Category = "Pricing" },
            new { CustomerName = "Henry Davis", Comment = "Terrible experience with the mobile app. It crashes frequently and is very buggy.", Rating = 1, Category = "Performance" },
            new { CustomerName = "Ivy Wilson", Comment = "Great customer service! The support team is knowledgeable and friendly.", Rating = 5, Category = "Support" },
            new { CustomerName = "Jack Brown", Comment = "The web platform is okay but could be more intuitive. Some features are hard to find.", Rating = 3, Category = "User Experience" },
            new { CustomerName = "Kelly Johnson", Comment = "Excellent product functionality! Everything works as expected and more.", Rating = 5, Category = "Product Functionality" },
            new { CustomerName = "Liam Smith", Comment = "Delivery was fast and efficient. The package was well-protected and arrived on time.", Rating = 4, Category = "Service" },
            new { CustomerName = "Mia Davis", Comment = "The payment system is secure and easy to use. No issues with transactions.", Rating = 4, Category = "Product Functionality" },
            new { CustomerName = "Noah Wilson", Comment = "Poor quality product. It broke after just a few days of use. Not worth the money.", Rating = 2, Category = "Quality" },
            new { CustomerName = "Olivia Brown", Comment = "Amazing user experience! The interface is clean and everything is easy to navigate.", Rating = 5, Category = "User Experience" },
            new { CustomerName = "Paul Miller", Comment = "Good service overall but the pricing could be more competitive compared to alternatives.", Rating = 3, Category = "Pricing" },
            new { CustomerName = "Quinn Taylor", Comment = "The mobile app has improved significantly. Much faster and more stable now.", Rating = 4, Category = "Performance" },
            new { CustomerName = "Rachel Davis", Comment = "Excellent support team! They went above and beyond to help me with my issue.", Rating = 5, Category = "Support" },
            new { CustomerName = "Sam Wilson", Comment = "The web platform is slow and outdated. Needs a complete redesign and better performance.", Rating = 2, Category = "Performance" },
            new { CustomerName = "Tina Brown", Comment = "Great product with excellent functionality. Easy to use and very reliable.", Rating = 5, Category = "Product Functionality" }
        };

        foreach (var feedbackData in sampleFeedbacks)
        {
            var randomProduct = products[Random.Shared.Next(products.Count)];
            var randomUser = users.Count > 0 ? users[Random.Shared.Next(users.Count)] : null;

            // Perform sentiment analysis
            var sentimentResult = await _sentimentService.AnalyzeSentimentAsync(feedbackData.Comment);
            var keywords = _sentimentService.ExtractKeywords(feedbackData.Comment);

            var feedback = new Feedback
            {
                CustomerName = feedbackData.CustomerName,
                ProductId = randomProduct.Id,
                Category = feedbackData.Category,
                Rating = feedbackData.Rating,
                Comment = feedbackData.Comment,
                Sentiment = sentimentResult.Sentiment,
                SentimentScore = sentimentResult.Score,
                Keywords = JsonConvert.SerializeObject(keywords),
                CreatedAt = DateTime.UtcNow.AddDays(-Random.Shared.Next(1, 90)),
                UserId = randomUser?.Id
            };

            _context.Feedbacks.Add(feedback);
        }

        await _context.SaveChangesAsync();
    }
}
