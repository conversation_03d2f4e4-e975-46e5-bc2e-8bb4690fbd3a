using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Identity;

namespace CustomerFeedbackAnalyzer.Models;

public class ErrorViewModel
{
    public string? RequestId { get; set; }

    public bool ShowRequestId => !string.IsNullOrEmpty(RequestId);
}

// Application User extending Identity User
public class ApplicationUser : IdentityUser
{
    [Required]
    [StringLength(100)]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string LastName { get; set; } = string.Empty;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation property
    public virtual ICollection<Feedback> Feedbacks { get; set; } = new List<Feedback>();
}

// Product/Service Model
public class Product
{
    public int Id { get; set; }

    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;

    [StringLength(500)]
    public string? Description { get; set; }

    [Required]
    [StringLength(100)]
    public string Category { get; set; } = string.Empty;

    public bool IsActive { get; set; } = true;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation property
    public virtual ICollection<Feedback> Feedbacks { get; set; } = new List<Feedback>();
}

// Feedback Model
public class Feedback
{
    public int Id { get; set; }

    [Required]
    [StringLength(100)]
    public string CustomerName { get; set; } = string.Empty;

    [Required]
    public int ProductId { get; set; }

    [Required]
    [StringLength(100)]
    public string Category { get; set; } = string.Empty;

    [Required]
    [Range(1, 5)]
    public int Rating { get; set; }

    [Required]
    [StringLength(2000)]
    public string Comment { get; set; } = string.Empty;

    // Sentiment Analysis Results
    [StringLength(20)]
    public string? Sentiment { get; set; } // Positive, Neutral, Negative

    public double? SentimentScore { get; set; }

    [StringLength(1000)]
    public string? Keywords { get; set; } // JSON array of extracted keywords

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Foreign Keys
    public string? UserId { get; set; }

    // Navigation properties
    public virtual Product Product { get; set; } = null!;
    public virtual ApplicationUser? User { get; set; }
}
