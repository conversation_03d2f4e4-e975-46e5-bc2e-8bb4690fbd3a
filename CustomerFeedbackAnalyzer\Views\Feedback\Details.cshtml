@model CustomerFeedbackAnalyzer.Models.Feedback
@{
    ViewData["Title"] = "Feedback Details";
}

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-comment-dots me-2"></i>Feedback Details
                    </h4>
                    <div>
                        <a asp-action="List" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>Back to List
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <!-- Customer Information -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">Customer Information</h6>
                        <div class="border rounded p-3 bg-light">
                            <div class="mb-2">
                                <strong><i class="fas fa-user me-2"></i>Name:</strong> @Model.CustomerName
                            </div>
                            @if (Model.User != null)
                            {
                                <div class="mb-2">
                                    <strong><i class="fas fa-envelope me-2"></i>Email:</strong> @Model.User.Email
                                </div>
                                <div>
                                    <strong><i class="fas fa-calendar me-2"></i>Member Since:</strong> @Model.User.CreatedAt.ToString("MMM dd, yyyy")
                                </div>
                            }
                            else
                            {
                                <div class="text-muted">
                                    <i class="fas fa-user-slash me-2"></i>Guest User
                                </div>
                            }
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">Feedback Information</h6>
                        <div class="border rounded p-3 bg-light">
                            <div class="mb-2">
                                <strong><i class="fas fa-box me-2"></i>Product/Service:</strong> @Model.Product?.Name
                            </div>
                            <div class="mb-2">
                                <strong><i class="fas fa-tags me-2"></i>Category:</strong> @Model.Category
                            </div>
                            <div>
                                <strong><i class="fas fa-calendar-alt me-2"></i>Submitted:</strong> @Model.CreatedAt.ToString("MMM dd, yyyy HH:mm")
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Rating and Sentiment -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">Rating</h6>
                        <div class="text-center p-3 border rounded bg-light">
                            <div class="display-4 text-primary mb-2">@Model.Rating</div>
                            <div class="text-warning fs-4">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    <i class="fas fa-star @(i <= Model.Rating ? "" : "text-muted")"></i>
                                }
                            </div>
                            <div class="text-muted mt-2">out of 5 stars</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">Sentiment Analysis</h6>
                        <div class="text-center p-3 border rounded bg-light">
                            <div class="mb-3">
                                <span class="badge fs-6 @(Model.Sentiment == "Positive" ? "bg-success" : Model.Sentiment == "Negative" ? "bg-danger" : "bg-warning")">
                                    <i class="fas @(Model.Sentiment == "Positive" ? "fa-smile" : Model.Sentiment == "Negative" ? "fa-frown" : "fa-meh") me-2"></i>
                                    @Model.Sentiment
                                </span>
                            </div>
                            @if (Model.SentimentScore.HasValue)
                            {
                                <div class="text-muted">
                                    Score: @Model.SentimentScore.Value.ToString("F3")
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <!-- Comment -->
                <div class="mb-4">
                    <h6 class="text-muted mb-2">Feedback Comment</h6>
                    <div class="border rounded p-3 bg-light">
                        <p class="mb-0 lh-lg">@Model.Comment</p>
                    </div>
                </div>

                <!-- Keywords -->
                @if (!string.IsNullOrEmpty(Model.Keywords))
                {
                    <div class="mb-4">
                        <h6 class="text-muted mb-2">Extracted Keywords</h6>
                        <div class="border rounded p-3 bg-light">
                            @try
                            {
                                var keywords = Newtonsoft.Json.JsonConvert.DeserializeObject<List<string>>(Model.Keywords);
                                if (keywords != null && keywords.Any())
                                {
                                    foreach (var keyword in keywords)
                                    {
                                        <span class="badge bg-secondary me-2 mb-2">@keyword</span>
                                    }
                                }
                                else
                                {
                                    <span class="text-muted">No keywords extracted</span>
                                }
                            }
                            catch
                            {
                                <span class="text-muted">Unable to parse keywords</span>
                            }
                        </div>
                    </div>
                }

                <!-- Actions -->
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <a asp-action="List" class="btn btn-outline-primary">
                            <i class="fas fa-list me-1"></i>Back to All Feedback
                        </a>
                        <a asp-controller="Dashboard" asp-action="Index" class="btn btn-outline-info">
                            <i class="fas fa-chart-bar me-1"></i>View Dashboard
                        </a>
                    </div>
                    @if (User.Identity?.IsAuthenticated == true)
                    {
                        <div>
                            <button type="button" class="btn btn-outline-danger" onclick="deleteFeedback(@Model.Id)">
                                <i class="fas fa-trash me-1"></i>Delete Feedback
                            </button>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function deleteFeedback(id) {
            if (confirm('Are you sure you want to delete this feedback? This action cannot be undone.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '@Url.Action("Delete", "Feedback")';
                
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'id';
                input.value = id;
                
                const token = document.createElement('input');
                token.type = 'hidden';
                token.name = '__RequestVerificationToken';
                token.value = $('input[name="__RequestVerificationToken"]').val();
                
                form.appendChild(input);
                form.appendChild(token);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
}
