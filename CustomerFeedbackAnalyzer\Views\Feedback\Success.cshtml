@{
    ViewData["Title"] = "Feedback Submitted";
}

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow border-success">
            <div class="card-header bg-success text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-check-circle me-2"></i>Thank You!
                </h4>
            </div>
            <div class="card-body text-center p-5">
                <div class="mb-4">
                    <i class="fas fa-thumbs-up text-success" style="font-size: 4rem;"></i>
                </div>
                
                <h5 class="card-title text-success mb-3">Feedback Submitted Successfully!</h5>
                
                <p class="card-text text-muted mb-4">
                    Your feedback has been received and will be analyzed to help us improve our products and services. 
                    We truly appreciate you taking the time to share your experience with us.
                </p>
                
                <div class="alert alert-info" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>What happens next?</strong><br>
                    Your feedback will be processed through our sentiment analysis system and included in our dashboard analytics to help our team make data-driven improvements.
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                    <a asp-controller="Feedback" asp-action="Create" class="btn btn-outline-primary me-md-2">
                        <i class="fas fa-plus me-1"></i>Submit Another Feedback
                    </a>
                    <a asp-controller="Home" asp-action="Index" class="btn btn-primary">
                        <i class="fas fa-home me-1"></i>Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
