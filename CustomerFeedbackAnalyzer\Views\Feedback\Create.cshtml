@model CustomerFeedbackAnalyzer.Models.FeedbackViewModel
@{
    ViewData["Title"] = "Submit Feedback";
}

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h4 class="mb-0">
                    <i class="fas fa-comments me-2"></i>Submit Your Feedback
                </h4>
                <p class="mb-0 mt-2">We value your opinion! Please share your experience with us.</p>
            </div>
            <div class="card-body p-4">
                <form asp-action="Create" method="post">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="CustomerName" class="form-label required">
                                <i class="fas fa-user me-1"></i>Your Name
                            </label>
                            <input asp-for="CustomerName" class="form-control" placeholder="Enter your full name" />
                            <span asp-validation-for="CustomerName" class="text-danger"></span>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label asp-for="ProductId" class="form-label required">
                                <i class="fas fa-box me-1"></i>Product/Service
                            </label>
                            <select asp-for="ProductId" class="form-select" asp-items="@(new SelectList(Model.Products, "Id", "Name"))">
                                <option value="">-- Select Product/Service --</option>
                            </select>
                            <span asp-validation-for="ProductId" class="text-danger"></span>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Category" class="form-label required">
                                <i class="fas fa-tags me-1"></i>Category
                            </label>
                            <select asp-for="Category" class="form-select" asp-items="@(new SelectList(Model.Categories))">
                                <option value="">-- Select Category --</option>
                            </select>
                            <span asp-validation-for="Category" class="text-danger"></span>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label asp-for="Rating" class="form-label required">
                                <i class="fas fa-star me-1"></i>Rating
                            </label>
                            <div class="rating-container">
                                <div class="star-rating" id="starRating">
                                    <span class="star" data-rating="1">★</span>
                                    <span class="star" data-rating="2">★</span>
                                    <span class="star" data-rating="3">★</span>
                                    <span class="star" data-rating="4">★</span>
                                    <span class="star" data-rating="5">★</span>
                                </div>
                                <input asp-for="Rating" type="hidden" id="ratingInput" />
                                <div class="rating-text mt-1" id="ratingText">Click stars to rate</div>
                            </div>
                            <span asp-validation-for="Rating" class="text-danger"></span>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label asp-for="Comment" class="form-label required">
                            <i class="fas fa-comment me-1"></i>Your Feedback
                        </label>
                        <textarea asp-for="Comment" class="form-control" rows="5" 
                                  placeholder="Please share your detailed feedback here. Minimum 10 characters required."></textarea>
                        <div class="form-text">
                            <span id="charCount">0</span> / 2000 characters (minimum 10 required)
                        </div>
                        <span asp-validation-for="Comment" class="text-danger"></span>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a asp-controller="Home" asp-action="Index" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-paper-plane me-1"></i>Submit Feedback
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    .required::after {
        content: " *";
        color: red;
    }
    
    .star-rating {
        font-size: 2rem;
        color: #ddd;
        cursor: pointer;
        user-select: none;
    }
    
    .star-rating .star {
        transition: color 0.2s;
    }
    
    .star-rating .star:hover,
    .star-rating .star.active {
        color: #ffc107;
    }
    
    .rating-text {
        font-size: 0.9rem;
        color: #6c757d;
    }
</style>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            // Star rating functionality
            let selectedRating = 0;
            
            $('.star').on('click', function() {
                selectedRating = $(this).data('rating');
                $('#ratingInput').val(selectedRating);
                updateStars();
                updateRatingText();
            });
            
            $('.star').on('mouseenter', function() {
                const hoverRating = $(this).data('rating');
                highlightStars(hoverRating);
            });
            
            $('#starRating').on('mouseleave', function() {
                updateStars();
            });
            
            function highlightStars(rating) {
                $('.star').each(function(index) {
                    if (index < rating) {
                        $(this).addClass('active');
                    } else {
                        $(this).removeClass('active');
                    }
                });
            }
            
            function updateStars() {
                highlightStars(selectedRating);
            }
            
            function updateRatingText() {
                const texts = ['', 'Poor', 'Fair', 'Good', 'Very Good', 'Excellent'];
                $('#ratingText').text(texts[selectedRating] || 'Click stars to rate');
            }
            
            // Character counter
            $('#Comment').on('input', function() {
                const length = $(this).val().length;
                $('#charCount').text(length);
                
                if (length < 10) {
                    $('#charCount').parent().removeClass('text-success').addClass('text-danger');
                } else {
                    $('#charCount').parent().removeClass('text-danger').addClass('text-success');
                }
            });
            
            // Form validation
            $('form').on('submit', function(e) {
                if (selectedRating === 0) {
                    e.preventDefault();
                    alert('Please select a rating before submitting.');
                    return false;
                }
                
                const comment = $('#Comment').val().trim();
                if (comment.length < 10) {
                    e.preventDefault();
                    alert('Please provide at least 10 characters in your comment.');
                    return false;
                }
            });
        });
    </script>
}
