@model CustomerFeedbackAnalyzer.Models.LoginViewModel
@{
    ViewData["Title"] = "Login";
}

<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-sign-in-alt me-2"></i>Login
                </h4>
            </div>
            <div class="card-body p-4">
                <form asp-action="Login" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                    
                    <div class="mb-3">
                        <label asp-for="Email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>Email Address
                        </label>
                        <input asp-for="Email" class="form-control" placeholder="Enter your email" />
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Password" class="form-label">
                            <i class="fas fa-lock me-1"></i>Password
                        </label>
                        <input asp-for="Password" class="form-control" placeholder="Enter your password" />
                        <span asp-validation-for="Password" class="text-danger"></span>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input asp-for="RememberMe" class="form-check-input" />
                        <label asp-for="RememberMe" class="form-check-label">
                            Remember me
                        </label>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-1"></i>Login
                        </button>
                    </div>
                </form>
                
                <hr class="my-4">
                
                <div class="text-center">
                    <p class="mb-0">Don't have an account?</p>
                    <a asp-action="Register" class="btn btn-outline-primary">
                        <i class="fas fa-user-plus me-1"></i>Create Account
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
